#!/bin/bash

# 批量执行 kops 集群命令脚本
# 用于批量更新集群的 ingress 和 service 镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 集群列表
CLUSTERS=(
    "cls-c4wxs2uu"
    "cls-1i62zg8m"
    "cls-394m4jli"
    "cls-3daxhf1b"
    "cls-6aik1zq5"
    "cls-ddqdacw2"
    "cls-fsiq95a4"
    "cls-fve5sbyo"
    "cls-gkxk91km"
    "cls-i9et79ll"
    "cls-ltvaugce"
    "cls-mg7ol3cm"
    "cls-ogsfrmms"
    "cls-pz4wglp7"
    "cls-q8axllhf"
)

# 镜像配置
INGRESS_IMAGE="ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.6.0"
SERVICE_IMAGE="ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.6.0"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 kops 命令是否存在
check_kops() {
    if ! command -v kops &> /dev/null; then
        log_error "kops 命令未找到，请确保已安装 kops"
        exit 1
    fi
    log_info "kops 命令检查通过"
}

# 执行单个集群的更新
update_cluster() {
    local cluster=$1
    local success=true
    
    log_info "开始更新集群: $cluster"
    
    # 更新 ingress 镜像
    log_info "  更新 ingress 镜像..."
    if kops ingress setimage --cluster="$cluster" --image="$INGRESS_IMAGE"; then
        log_success "  ingress 镜像更新成功"
    else
        log_error "  ingress 镜像更新失败"
        success=false
    fi
    
    # 更新 service 镜像
    log_info "  更新 service 镜像..."
    if kops service setimage --cluster="$cluster" --image="$SERVICE_IMAGE"; then
        log_success "  service 镜像更新成功"
    else
        log_error "  service 镜像更新失败"
        success=false
    fi
    
    if $success; then
        log_success "集群 $cluster 更新完成"
    else
        log_error "集群 $cluster 更新失败"
    fi
    
    echo "----------------------------------------"
    return $success
}

# 显示帮助信息
show_help() {
    cat << EOF
批量 kops 集群更新脚本

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -d, --dry-run       仅显示将要执行的命令，不实际执行
    -c, --cluster       指定单个集群进行更新
    -l, --list          列出所有集群
    --ingress-image     指定 ingress 镜像 (默认: $INGRESS_IMAGE)
    --service-image     指定 service 镜像 (默认: $SERVICE_IMAGE)

示例:
    $0                                    # 更新所有集群
    $0 -c cls-c4wxs2uu                   # 更新单个集群
    $0 -d                                 # 干运行模式
    $0 --ingress-image custom:v1.0.0     # 使用自定义镜像

EOF
}

# 列出所有集群
list_clusters() {
    log_info "集群列表:"
    for cluster in "${CLUSTERS[@]}"; do
        echo "  - $cluster"
    done
}

# 干运行模式
dry_run() {
    log_warning "干运行模式 - 仅显示将要执行的命令"
    echo
    
    for cluster in "${CLUSTERS[@]}"; do
        echo "集群: $cluster"
        echo "  kops ingress setimage --cluster=$cluster --image=$INGRESS_IMAGE"
        echo "  kops service setimage --cluster=$cluster --image=$SERVICE_IMAGE"
        echo
    done
}

# 主函数
main() {
    local dry_run_mode=false
    local single_cluster=""
    local list_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--dry-run)
                dry_run_mode=true
                shift
                ;;
            -c|--cluster)
                single_cluster="$2"
                shift 2
                ;;
            -l|--list)
                list_only=true
                shift
                ;;
            --ingress-image)
                INGRESS_IMAGE="$2"
                shift 2
                ;;
            --service-image)
                SERVICE_IMAGE="$2"
                shift 2
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 仅列出集群
    if $list_only; then
        list_clusters
        exit 0
    fi
    
    # 干运行模式
    if $dry_run_mode; then
        dry_run
        exit 0
    fi
    
    # 检查 kops 命令
    check_kops
    
    log_info "开始批量更新集群"
    log_info "Ingress 镜像: $INGRESS_IMAGE"
    log_info "Service 镜像: $SERVICE_IMAGE"
    echo "========================================"
    
    local failed_clusters=()
    local success_count=0
    local total_count=0
    
    # 处理单个集群或所有集群
    if [[ -n "$single_cluster" ]]; then
        # 检查集群是否在列表中
        if [[ " ${CLUSTERS[*]} " =~ " $single_cluster " ]]; then
            total_count=1
            if update_cluster "$single_cluster"; then
                ((success_count++))
            else
                failed_clusters+=("$single_cluster")
            fi
        else
            log_error "集群 $single_cluster 不在预定义列表中"
            exit 1
        fi
    else
        # 更新所有集群
        total_count=${#CLUSTERS[@]}
        for cluster in "${CLUSTERS[@]}"; do
            if update_cluster "$cluster"; then
                ((success_count++))
            else
                failed_clusters+=("$cluster")
            fi
        done
    fi
    
    # 显示总结
    echo "========================================"
    log_info "更新完成"
    log_success "成功: $success_count/$total_count"
    
    if [[ ${#failed_clusters[@]} -gt 0 ]]; then
        log_error "失败的集群:"
        for cluster in "${failed_clusters[@]}"; do
            echo "  - $cluster"
        done
        exit 1
    else
        log_success "所有集群更新成功！"
    fi
}

# 执行主函数
main "$@"
